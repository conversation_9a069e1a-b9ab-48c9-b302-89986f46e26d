package org.example;

import org.example.dao.BookDao;
import org.example.dao.impl.BookDaoImpl;
import org.example.service.BookService;
import org.example.service.impl.BookServiceImpl;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

public class Main {
    public static void main(String[] args) {

        ClassPathXmlApplicationContext c = new ClassPathXmlApplicationContext("applicationContext.xml");

        // 测试DAO
        System.out.println("=== 测试DAO ===");
        BookDao bookDao = (BookDao) c.getBean("bookDao");
        bookDao.save();

        // 测试Service（会自动注入DAO）
        System.out.println("\n=== 测试Service ===");
        BookService bookService = (BookService) c.getBean("bookService");
        bookService.save();

        c.close();

    }
}