package org.example;

import org.example.dao.BookDao;
import org.example.dao.impl.BookDaoImpl;
import org.example.service.BookService;
import org.example.service.impl.BookServiceImpl;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

public class Main {
    public static void main(String[] args) {

        ClassPathXmlApplicationContext c = new ClassPathXmlApplicationContext("applicationContext.xml");
        BookDao bookDao=(BookDao) c.getBean("bookDao");
        bookDao.save();
        c.close();//必须关闭容器

    }
}