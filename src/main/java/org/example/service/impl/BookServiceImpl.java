package org.example.service.impl;

import org.example.dao.BookDao;
import org.example.service.BookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BookServiceImpl implements BookService {

    @Autowired
    private BookDao bookDao;
//    public void setBookDao(BookDao bookDao)
//    {
//        this.bookDao = bookDao;
//    }
//    有注解就不需要setter方法
    public void save()
    {
        System.out.println("BookServiceImpl.save()");
        bookDao.save();
    }
    public void destory()
    {
        System.out.println("BookServiceImpl.destory()");
        bookDao.destory();
    }
    public void afterPropertiesSet() throws Exception
    {
        System.out.println("service init");
    }

}
