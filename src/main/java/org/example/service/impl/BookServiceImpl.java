package org.example.service.impl;

import org.example.dao.BookDao;
import org.example.service.BookService;
import org.springframework.stereotype.Service;

@Service
public class BookServiceImpl implements BookService {

    private BookDao bookDao;
    public void setBookDao(BookDao bookDao)
    {
        this.bookDao = bookDao;
    }
    public void save()
    {
        System.out.println("BookServiceImpl.save()");
        bookDao.save();
    }
    public void destory()
    {
        System.out.println("BookServiceImpl.destory()");
        bookDao.destory();
    }
    public void afterPropertiesSet() throws Exception
    {
        System.out.println("service init");
    }

}
