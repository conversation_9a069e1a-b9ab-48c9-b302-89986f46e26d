package org.example.dao.impl;

import org.example.dao.BookDao;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

@Repository
public class BookDaoImpl implements BookDao {
    private int[] array;//数组属性
    private List<String> list;//集合属性

    @Override
    public String toString() {
        return "BookDaoImpl{" +
                "array=" + Arrays.toString(array) +
                '}';
    }

    public void setArray(int[] array) {
        this.array = array;
    }

    public void setList(List<String> list) {
        this.list = list;
    }

    public void save() {
        System.out.println("array = " + Arrays.toString(array));
        System.out.println("list = " + list);
        System.out.println("保存图书成功");
    }
    //bean 的初始化方法
    public void init(){
        System.out.println("初始化方法");
    }
    //bean 的销毁方法
    public void destory(){
        System.out.println("销毁方法");
    }

}
